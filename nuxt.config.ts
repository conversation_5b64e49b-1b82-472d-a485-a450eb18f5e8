// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2024-08-03",
  devtools: { enabled: false },
  ssr: false,
  app: {
    head: {
      title: "<PERSON><PERSON>",
      meta: [
        {
          name: "description",
          content: "",
        },
      ],
      htmlAttrs: {
        lang: "en",
      },
      // script: [
      //   {
      //     async: true,
      //     src: "/js/xScrubScroll.js",
      //   },
      // ],
    },
  },
  css: ["@/assets/css/froala-editor.css"],
  runtimeConfig: {
    public: {
      apiURL: process.env.NUXT_PUBLIC_API_URL,
    },
  },
  nitro: {
    devProxy: {
      "/api/": {
        target: "https://api-shawnigan.up.railway.app/public",
        changeOrigin: true,
        prependPath: true,
      },
    },
  },
  colorMode: {
    preference: "light",
  },
  extends: ["@nuxt/ui-pro"],
  modules: [
    "@nuxt/content",
    "@nuxt/ui",
    "@nuxtjs/google-fonts",
    "@formkit/nuxt",
  ],

  googleFonts: {
    families: { Lato: true },
  },

  ui: {
    icons: ["heroicons", "simple-icons", "iconoir"],
  },
});
