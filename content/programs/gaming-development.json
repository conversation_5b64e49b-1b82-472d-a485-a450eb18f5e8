{"name": "Gaming Development", "slug": "gaming-development", "photo": "https://picsum.photos/800/600?random=1", "sections": ["header", "description", "video"], "description": "Create and design video games, learning about game mechanics, storytelling, character design, and interactive environments.", "careers": ["Game Developer", "Game Designer", "Level Designer", "3D Artist", "Game Tester"], "skills": ["Game Programming", "Level Design", "3D Modeling", "Game Engine Proficiency (e.g., Unity, Unreal Engine)", "Interactive Storytelling"], "lifeSkills": ["Creativity", "Problem-Solving", "Team Collaboration", "Attention to Detail", "Project Management"], "problem": "The gaming industry is one of the fastest-growing sectors in entertainment, yet many aspiring game developers lack access to comprehensive education that combines both the technical and creative aspects of game creation. Traditional programs often focus heavily on programming or design without providing a balanced approach that covers the full development pipeline.", "refinement": "Our Gaming Development program offers a well-rounded education that integrates game design, programming, and artistic creation. Students will gain hands-on experience with industry-standard tools and game engines, working on projects that mirror real-world game development scenarios. This program ensures that graduates are prepared to enter the gaming industry with a strong foundation in both the technical and creative skills needed to succeed.", "audience": "This program is ideal for aspiring game developers, designers, and artists who are passionate about creating interactive experiences. Whether you are new to game development or have some experience, this program provides the comprehensive training needed to excel in the competitive gaming industry.", "testimonials": [{"name": "<PERSON>", "title": "Game Developer, Ubisoft", "quote": "The Gaming Development program at Shawnigan was the perfect blend of creativity and technical training. The projects I worked on during the program helped me build a strong portfolio that landed me my dream job in the gaming industry."}, {"name": "<PERSON>", "title": "Level Designer, EA Games", "quote": "This program gave me a deep understanding of level design and game mechanics. The instructors were industry professionals who provided invaluable insights into the game development process."}, {"name": "<PERSON>", "title": "3D Artist, Epic Games", "quote": "The hands-on experience with 3D modeling and game engines was incredibly beneficial. The program's focus on both the artistic and technical sides of game development has made me a more versatile and skilled 3D artist."}], "video": "https://shawnigan.getx.io/(A)%20Gaming%20Development%20V1.mp4", "photogallery": ["https://picsum.photos/1920/1080?random=1", "https://picsum.photos/1920/1080?random=2", "https://picsum.photos/1920/1080?random=3", "https://picsum.photos/1920/1080?random=4", "https://picsum.photos/1920/1080?random=5", "https://picsum.photos/1920/1080?random=6"], "features": {"headline": "Features", "title": "Why choose our Gaming Development program?", "description": "Create and design video games, learning about game mechanics, storytelling, character design, and interactive environments.", "items": [{"title": "Comprehensive Curriculum", "description": "Learn all aspects of game development, from mechanics to storytelling.", "icon": "i-heroicons-cog"}, {"title": "Reliable Engines", "description": "Use reliable game engines and development tools.", "icon": "i-heroicons-check"}, {"title": "Secure Code", "description": "Implement secure coding practices for game development.", "icon": "i-heroicons-lock-closed"}, {"title": "Fast Prototyping", "description": "Develop fast prototyping skills for game concepts.", "icon": "i-heroicons-rocket-launch"}, {"title": "Affordable Learning", "description": "Access cost-effective resources for game development.", "icon": "i-heroicons-currency-dollar"}, {"title": "Scalable Designs", "description": "Create scalable game designs adaptable to various platforms.", "icon": "i-heroicons-chart-bar"}]}}