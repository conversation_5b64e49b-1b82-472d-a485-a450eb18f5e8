{"name": "Robotics", "slug": "robotics", "photo": "https://picsum.photos/800/600?random=1", "sections": ["header", "description", "video"], "video": "https://shawnigan.getx.io/(B)%20Robotics%20Loop%20V1.mp4", "photogallery": ["https://picsum.photos/1920/1080?random=1", "https://picsum.photos/1920/1080?random=2", "https://picsum.photos/1920/1080?random=3", "https://picsum.photos/1920/1080?random=4", "https://picsum.photos/1920/1080?random=5", "https://picsum.photos/1920/1080?random=6"], "description": "Explore the field of robotics, including the design, construction, and programming of robots for various applications.", "careers": ["Robotics Engineer", "Automation Specialist", "Mechatronics Engineer", "AI Developer", "Research Scientist in Robotics"], "skills": ["Robotics Design", "Programming (e.g., Python, C++)", "Mechanical Engineering", "Artificial Intelligence", "Control Systems"], "lifeSkills": ["Problem-Solving", "Critical Thinking", "Innovation", "Team Collaboration", "Project Management"], "problem": "As robotics technology continues to advance and integrate into various industries, there is a growing demand for professionals who are proficient in both the engineering and programming aspects of robotics. However, many educational programs focus narrowly on one area, leaving graduates unprepared for the interdisciplinary challenges of modern robotics.", "refinement": "Our Robotics program is designed to provide a comprehensive education that bridges the gap between mechanical engineering, programming, and artificial intelligence. Students will engage in hands-on projects that involve designing, building, and programming robots, preparing them for careers in a rapidly evolving field. The program emphasizes both technical skills and creative problem-solving, ensuring that graduates are equipped to innovate in robotics.", "audience": "This program is ideal for aspiring robotics engineers, AI developers, and technology enthusiasts who are passionate about the future of automation and robotics. Whether you are a beginner or have some experience, this program provides the multidisciplinary training needed to succeed in the robotics industry.", "testimonials": [{"name": "<PERSON>", "title": "Robotics Engineer, Boston Dynamics", "quote": "The Robotics program at Shawnigan was instrumental in my career development. The hands-on projects and interdisciplinary approach gave me the skills and confidence to work on cutting-edge robotics technology."}, {"name": "<PERSON>", "title": "Automation Specialist, Tesla", "quote": "This program provided a solid foundation in both the engineering and programming aspects of robotics. The real-world applications and collaborative environment prepared me well for my role in automation."}, {"name": "<PERSON>", "title": "AI Developer, NVIDIA", "quote": "The focus on artificial intelligence and control systems was particularly beneficial. The instructors were experts in their fields, and the projects challenged us to push the boundaries of what robots can do."}], "features": {"headline": "Features", "title": "Why choose our Robotics program?", "description": "Explore the field of robotics, including the design, construction, and programming of robots for various applications.", "items": [{"title": "Innovative Design", "description": "Learn innovative techniques for designing and building robots.", "icon": "i-heroicons-cog"}, {"title": "Reliable Construction", "description": "Construct reliable and functional robotic systems.", "icon": "i-heroicons-check"}, {"title": "Secure Programming", "description": "Understand secure practices for programming robots.", "icon": "i-heroicons-lock-closed"}, {"title": "Fast Development", "description": "Accelerate development with hands-on projects.", "icon": "i-heroicons-rocket-launch"}, {"title": "Affordable Materials", "description": "Access affordable materials and components for robotics.", "icon": "i-heroicons-currency-dollar"}, {"title": "Scalable Applications", "description": "Develop scalable robotics solutions for various industries.", "icon": "i-heroicons-chart-bar"}]}}