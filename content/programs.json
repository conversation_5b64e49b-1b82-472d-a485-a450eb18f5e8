[{"name": "Broadcast Journalism", "path": "/programs/broadcast-journalism", "icon": "i-iconoir-tv", "description": "Learn the skills of news reporting, interviewing, and producing broadcast content for radio, television, and digital media."}, {"name": "Coding", "path": "/programs/coding", "icon": "i-iconoir-code", "description": "Develop programming skills and learn various coding languages to build software, applications, and websites."}, {"name": "Digital Imagery", "path": "/programs/digital-imagery", "icon": "i-iconoir-camera", "description": "Explore the world of digital art and photography, focusing on image editing, graphic design, and visual storytelling."}, {"name": "Drama Theater", "path": "/programs/drama-theater", "icon": "i-iconoir-community", "description": "Immerse yourself in the performing arts with courses in acting, directing, stage design, and theater production."}, {"name": "Drone Development", "path": "/programs/drone-development", "icon": "i-iconoir-drone", "description": "Learn to design, build, and operate drones, with applications in photography, surveying, and various industries."}, {"name": "Film Making", "path": "/programs/film-making", "icon": "i-iconoir-video-camera", "description": "Study the art of film production, including screenwriting, cinematography, editing, and directing short and feature films."}, {"name": "Fine Arts", "path": "/programs/fine-arts", "icon": "i-iconoir-design-nib", "description": "Develop your artistic skills in drawing, painting, sculpture, and other traditional and contemporary fine arts practices."}, {"name": "Gaming Development", "path": "/programs/gaming-development", "icon": "i-iconoir-playstation-gamepad", "description": "Create and design video games, learning about game mechanics, storytelling, character design, and interactive environments."}, {"name": "Photography", "path": "/programs/photography", "icon": "i-iconoir-camera", "description": "Master the art of photography, from camera techniques and lighting to composition and post-processing in both digital and film formats."}, {"name": "Recording Arts", "path": "/programs/recording-arts", "icon": "i-iconoir-microphone", "description": "Learn the technical and creative aspects of sound recording, mixing, and production in music, film, and other media."}, {"name": "Robotics", "path": "/programs/robotics", "icon": "i-iconoir-electronics-chip", "description": "Explore the field of robotics, including the design, construction, and programming of robots for various applications."}, {"name": "VOTEC", "path": "/programs/votec", "icon": "i-iconoir-tools", "description": "Gain practical skills in vocational and technical education, preparing for careers in trades, engineering, and applied sciences."}]