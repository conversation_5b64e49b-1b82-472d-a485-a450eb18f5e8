/** @type {import('tailwindcss').Config} */
export default {
  darkMode: 'class',
  content: [
  ],
  theme: {
    extend: {
      transitionDelay: {
        '100': '100ms',
        '200': '200ms',
        '300': '300ms',
        '400': '400ms',
        '500': '500ms',
        '600': '600ms',
        '700': '700ms',
        '800': '800ms',
        '900': '900ms',
        '1000': '1000ms',
        '1100': '1100ms',
        '1200': '1200ms',
        '1300': '1300ms',
      },
      colors: {
        'supernova': { DEFAULT: '#FFCC00', 50: '#FFF1B8', 100: '#FFEDA3', 200: '#FFE47A', 300: '#FFDC52', 400: '#FFD429', 500: '#FFCC00', 600: '#C79F00', 700: '#8F7200', 800: '#574500', 900: '#1F1800', 950: '#030200' },
      },
      keyframes: {
        fadeUpShort: {
          '0%': { opacity: 0, transform: 'translateY(10px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
        fadeUp: {
          '0%': { opacity: 0, transform: 'translateY(20px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
      },
      animation: {
        fadeUp: 'fadeUp 1s ease-out forwards',
        fadeUpShort: 'fadeUpShort 0.5s ease-out forwards',
      },
      aspectRatio: {
        auto: 'auto',
        square: '1 / 1',
        video: '16 / 9'
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography')
  ],
}

