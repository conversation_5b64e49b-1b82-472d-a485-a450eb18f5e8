/* Base editor content styles - keeping <PERSON><PERSON><PERSON>'s default editor UI intact */
.fr-box.fr-basic {
  @apply rounded-lg border border-neutral-200 dark:border-neutral-700;
}

/* Content area styles */
.fr-view {
}

/* Lists - only target direct children to avoid affecting toolbar dropdowns */
.fr-view > ul {
  @apply list-disc ml-4 mb-4 space-y-1;
}

.fr-view > ol {
  @apply list-decimal ml-4 mb-4 space-y-1;
}

/* Nested lists */
.fr-view ul ul,
.fr-view ol ol,
.fr-view ul ol,
.fr-view ol ul {
  @apply ml-4 mb-0;
}

/* Headings */
.fr-view h1 {
  @apply text-3xl font-bold mb-4;
}

.fr-view h2 {
  @apply text-2xl font-bold mb-3;
}

.fr-view h3 {
  @apply text-xl font-bold mb-2;
}

/* Links */
.fr-view a {
  @apply underline;
}

/* Blockquotes */
.fr-view blockquote {
  @apply border-l-4 border-supernova-500 pl-4 italic my-4;
}

/* Tables */
.fr-view table {
  @apply w-full border-collapse mb-4;
}

.fr-view th,
.fr-view td {
  @apply border border-neutral-200 dark:border-neutral-700 p-2;
}

/* Images */
.fr-view img {
  @apply max-w-full h-auto rounded-lg;
}

/* Preserve Froala toolbar styling */
.fr-toolbar {
  @apply border border-neutral-200 dark:border-neutral-700 !important;
}

/* Preserve Froala popup styling */
.fr-popup {
  @apply border border-neutral-200 dark:border-neutral-700 !important;
}
