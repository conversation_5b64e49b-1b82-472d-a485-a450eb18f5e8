<template>
	<main class="dark w-full h-full relative z-30">
		<section class="pt-4">
			<div class="flex items-center justify-between max-w-screen-2xl mx-auto">
				<h3
					class="text-base font-semibold leading-6 text-gray-950 dark:text-white uppercase ml-4"
					:class="`transition transform duration-100 ease-out animate-fadeUp`"
				>
					<img src="/CASE roof horiz white.png" class="w-32 ml-1" />
					<em class="text-sm -mt-1 opacity-40"
						>Communications - Arts - Sciences - Entrepeneurship</em
					>
				</h3>
				<div
					class="text-white mr-8 text-center text-2xl opacity-40 font-black uppercase"
				>
					Powering the Shawnigan Journey
				</div>
			</div>
		</section>
		<section
			class="flex-grow grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 max-w-screen-2xl mx-auto p-4"
		>
			<article v-for="(program, index) in programs" :key="index">
				<ULandingCard
					:title="program?.name"
					:description="program?.navDescription"
					:to="`/programs/${program?.slug}`"
					:icon="program?.icon"
					@click="isOpen = false"
					class="fadeUp"
					:ui="{
						background: 'bg-opacity-10',
					}"
					:style="{
						animationDelay: `${index * 75}ms`,
					}"
				>
					<div
						v-if="program.isComingSoon == true"
						class="absolute top-5 right-5 bg-gray-500 text-white px-2 py-1 rounded"
					>
						COMING SOON
					</div>
				</ULandingCard>
			</article>
		</section>
	</main>
</template>
<script setup lang="ts">
import { _opacity } from "#tailwind-config/theme/transitionProperty";

const isOpen = ref(true);
const programs = await usePrograms().getAll();
console.log(programs);
</script>
