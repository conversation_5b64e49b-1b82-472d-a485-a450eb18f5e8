<template>
  <!-- Top Bar -->
  <!-- <USlideover
    v-model="isSlideoverOpen"
    side="top"
    :ui="{
      height: 'max-h-36',
      transition: {
        enter: 'transform transition ease-in-out duration-700',
        leave: 'transform transition ease-in-out duration-700',
      },
    }"
  > -->
  <nav
    class="bg-black text-[#D8D8D8] flex flex-row justify-end items-center pr-4 space-x-5 text-[15px] font-medium py-0.5 tracking-loose relative z-50"
  >
    <a
      href="https://shawnigan.myschoolapp.com/app#login/apply"
      target="_blank"
      class="bg-[#c99700] text-white py-0.5 px-2 ring-none border-none"
      >APPLY NOW</a
    >
    <a
      href="//www.shawnigan.ca/page/about/contact"
      class="hover:text-[#c99700] py-1"
      >CONTACT</a
    >
    <a href="//www.shawnigan.ca/app" class="hover:text-[#c99700]">LOGIN</a>
    <a
      href="//www.shawnigan.ca/page/a-voice-in-the-wilderness-blog"
      class="hover:text-[#c99700]"
      >BLOG</a
    >
  </nav>
  <nav class="bg-black bg-opacity-70 pt-1 pb-2 relative z-50 h-[110px]">
    <div class="flex justify-between items-center px-4">
      <div class="text-white text-lg">
        <nuxt-link to="https://www.shawnigan.ca/">
          <img src="/shawnigan_logo.png" alt="Shawnigan Lake School" />
        </nuxt-link>
      </div>
      <ul class="hidden md:flex space-x-2 lato nav">
        <li
          v-for="(item, index) in menuItems?.body"
          :key="item.label"
          class="relative group"
        >
          <a
            :href="
              item.to.startsWith('http')
                ? item.to
                : `https://www.shawnigan.ca/${item.to}`
            "
            class="text-white px-3 py-2 rounded-md hover:text-[#FFCC00]"
            >{{ item.label }}</a
          >
          <ul
            v-if="item.submenu"
            class="absolute mt-1 bg-[#3a3a3a] text-[#D8D8D8] shadow-md opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto group-hover:block transition-opacity duration-300 text-[14px] w-[200px]"
            :class="index + 2 >= menuItems?.body.length ? 'right-0' : 'left-0'"
          >
            <li
              v-for="subItem in item.submenu"
              :key="subItem.label"
              class="hover:text-[#FFCC00]"
            >
              <a
                :href="
                  subItem.to.startsWith('http')
                    ? subItem.to
                    : `https://www.shawnigan.ca/${subItem.to}`
                "
                class="block px-4 py-2"
                >{{ subItem.label }}</a
              >
            </li>
          </ul>
        </li>
      </ul>
      <div class="md:hidden">
        <button @click="isOpen = !isOpen" class="text-white">
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16m-7 6h7"
            ></path>
          </svg>
        </button>
      </div>
    </div>
    <div
      v-if="isOpen"
      class="md:hidden lato uppercase tracking-tighter font-bold"
    >
      <ul class="bg-[#3a3a3a]">
        <li
          v-for="item in menuItems?.body"
          :key="item.label"
          class="relative py-1"
        >
          <a
            @click="toggleSubMenu(item.label)"
            class="block text-white px-3 py-2 cursor-pointer hover:text-[#FFCC00]"
            >{{ item.label }}
            <span v-if="item.submenu" class="float-right">{{
              itemOpen === item.label ? "▼" : "▶"
            }}</span>
          </a>
          <ul v-if="itemOpen === item.label" class="bg-[#3a3a3a] pl-3">
            <li v-for="subItem in item.submenu" :key="subItem.label" class="">
              <a
                :href="
                  subItem.to.startsWith('http')
                    ? subItem.to
                    : `https://www.shawnigan.ca/${subItem.to}`
                "
                class="block px-4 py-3 text-white hover:text-[#FFCC00]"
                >{{ subItem.label }}</a
              >
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </nav>
  <!-- <div class="text-center">
    <UButton
      icon="i-heroicons-arrow-up"
      variant="solid"
      color="gray"
      size="xs"
      :ui="{ rounded: 'rounded-none' }"
      @click="isSlideoverOpen = false"
    />
  </div> -->
  <!-- </USlideover> -->
  <!-- <div
    class="text-center flex flex-row gap-2 justify-center items-center text-xs text-gray-400 uppercase lato bg-supernova-500 cursor-pointer round-bl round-br w-96 mx-auto"
    @click="isSlideoverOpen = true"
  >
    <span>Shawnigan</span>
    <UButton
      icon="i-heroicons-arrow-down"
      variant="solid"
      color="dark-gray"
      size="xs"
      :ui="{ rounded: 'rounded-none' }"
    />
    <span>Home</span>
  </div> -->
</template>

<script setup>
import { ref } from "vue";
const isSlideoverOpen = ref(true);
const isOpen = ref(false);
const itemOpen = ref("");

const toggleSubMenu = (label) => {
  itemOpen.value = itemOpen.value === label ? "" : label;
};
const appConfig = useAppConfig();
const menuItems = { body: appConfig.shawniganMenuItems };
</script>
<style>
.nav {
  font-family: "Lato", sans-serif;
  font-size: 16px;
  @apply text-white uppercase font-bold;
}
</style>
