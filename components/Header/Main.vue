<template>
  <header
    class="relative z-10 flex justify-between items-center p-4 fadeUpShort bg-white"
  >
    <div class="flex justify-center items-center gap-8 text-lg text-gray-600">
      <nuxt-link to="/">
        <div class="flex">
          <img src="/logo.jpeg" class="w-32" />
          <img src="/misc_172095.png" class="h-8 pl-2" alt="" />
        </div>
        <em class="text-sm -mt-1 opacity-50"
          >Communications - Arts - Sciences - Entrepeneurship</em
        >
      </nuxt-link>
      <nuxt-link to="/about" class="uppercase">About</nuxt-link>
      <nuxt-link to="/faq" class="uppercase">FAQ</nuxt-link>
    </div>

    <nav class="flex items-center gap-4">
      <!-- Knockout Text -->
      <div class="bg-cover bg-center hidden md:flex">
        <div class="text-2xl font-black text-gray-300 uppercase">
          Powering the Shawnigan Journey
        </div>
      </div>

      <HeaderNavigationModal />
    </nav>
  </header>
</template>
<style scoped>
.background {
  background-image: url("https://images.pexels.com/photos/797797/pexels-photo-797797.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=650&w=940");
}
</style>
