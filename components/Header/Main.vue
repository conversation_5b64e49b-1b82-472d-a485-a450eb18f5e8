<template>
  <header
    class="relative z-10 lg:flex justify-between items-center p-4 fadeUpShort bg-white"
  >
    <div
      class="md:flex justify-center items-center gap-8 text-lg text-gray-600"
    >
      <nuxt-link to="/">
        <div class="flex">
          <img src="/logo.jpeg" class="w-32" />
          <img src="/misc_172095.png" class="h-8 pl-2" alt="" />
        </div>
        <em class="text-sm -mt-1 opacity-50"
          >Communications - Arts - Sciences - Entrepeneurship</em
        >
      </nuxt-link>

      <div>
        <!-- about modal -->
        <UButton
          label="About"
          color="gray"
          variant="ghost"
          @click="isAboutOpen = true"
          class="uppercase text-lg"
        />
        <UModal v-model="isAboutOpen">
          <UCard class="lg:min-w-[700px]">
            <template #header>
              <h3 class="text-lg font-semibold">About CASE</h3>
            </template>
            <div class="p-4">
              <p>{{ about.value }}</p>
            </div>
            <template #footer>
              <div class="flex justify-end gap-2">
                <UButton label="Close" @click="isAboutOpen = false" />
              </div>
            </template>
          </UCard>
        </UModal>
        <UButton
          label="FAQ"
          color="gray"
          variant="ghost"
          @click="isFaqsOpen = true"
          class="uppercase text-lg"
        />
        <!-- faq modal -->
        <UModal v-model="isFaqsOpen">
          <UCard class="lg:min-w-[700px]">
            <template #header>
              <h3 class="text-lg font-semibold">FAQ</h3>
            </template>
            <div v-for="faq in faqs" :key="faq.id">
              <h2 class="font-bold py-4">{{ faq.label }}</h2>
              <p>{{ faq.content }}</p>
            </div>
            <template #footer>
              <div class="flex justify-end gap-2">
                <UButton label="Close" @click="isFaqsOpen = false" />
              </div>
            </template>
          </UCard>
        </UModal>
      </div>
    </div>

    <nav
      class="flex items-center justify-center lg:justify-end gap-4 mt-3 lg:mt-0"
    >
      <!-- Knockout Text -->
      <div class="bg-cover bg-center hidden md:flex">
        <div class="text-2xl font-black text-gray-300 uppercase text-right">
          Powering the Shawnigan Journey
        </div>
      </div>

      <HeaderNavigationModal />
    </nav>
  </header>
</template>

<script setup>
const isAboutOpen = ref(false);
const isFaqsOpen = ref(false);
const about = await useGeneral().getOne("about");
const faqs = await useFaqs().getAll();

console.log(faqs);
</script>

<style scoped>
.background {
  background-image: url("https://images.pexels.com/photos/797797/pexels-photo-797797.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=650&w=940");
}
</style>
