<template>
	<div>
		<UButton
			variant="ghost"
			color="black"
			icon="i-heroicons-bars-3-20-solid"
			@click="isOpen = true"
		/>
		<UModal v-model="isOpen" fullscreen>
			<UCard
				:ui="{
					base: 'h-full flex flex-col border-none',
					rounded: '',
					body: {
						base: 'grow border-none',
					},
					footer: {
						base: 'border-none',
					},
				}"
				class="dark bg-gray-900"
			>
				<template #header>
					<div
						class="flex items-center justify-between max-w-screen-2xl mx-auto"
					>
						<h3
							class="text-base font-semibold leading-6 text-gray-950 dark:text-white uppercase ml-4"
							:class="`transition transform duration-100 ease-out animate-fadeUp`"
						>
							<img src="/CASE roof horiz white.png" class="w-32 ml-1" />
							<em class="text-sm -mt-1 opacity-40"
								>Communications - Arts - Sciences - Entrepeneurship</em
							>
						</h3>
						<section class="flex flex-row justify-end">
							<!-- Knockout Text -->
							<div class="bg-cover bg-center hidden md:flex">
								<div class="text-2xl font-black text-gray-300 uppercase">
									Powering the Shawnigan Journey
								</div>
							</div>
							<UButton
								color="gray"
								variant="ghost"
								icon="i-heroicons-x-mark-20-solid"
								class="-my-1"
								@click="isOpen = false"
							/>
						</section>
					</div>
				</template>
				<section
					class="flex-grow grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 max-w-screen-2xl mx-auto p-4"
				>
					<article v-for="(program, index) in programs" :key="index">
						<ULandingCard
							:title="program?.name"
							:description="program?.navDescription"
							:to="`/programs/${program?.slug}`"
							:icon="program?.icon"
							@click="isOpen = false"
							class="fadeUp"
							:style="{
								animationDelay: `${index * 75}ms`,
							}"
						>
							<div
								v-if="program.isComingSoon == true"
								class="absolute top-5 right-5 bg-gray-500 text-white px-2 py-1 rounded"
							>
								COMING SOON
							</div>
						</ULandingCard>
					</article>
				</section>
				<template #footer>
					<div class="max-w-screen-2xl mx-auto mb-16 p-4">
						<ULandingCard
							title="Donate to C.A.S.E."
							description="Your generous contributions help us provide the best education and resources for our students. Learn more about how your donation can make a significant impact on our programs and facilities."
							:link="{ text: 'Learn More', to: '/brand/color-palette' }"
							to="https://www.shawnigan.ca/donate-to-case"
							target="_blank"
							class="items-center text-center fadeUp"
							:style="{
								animationDelay: `1750ms`,
							}"
						/>
					</div>
				</template>
			</UCard>
		</UModal>
	</div>
</template>
<script setup lang="ts">
const isOpen = ref(false);
const programs = await usePrograms().getAll();

// const donationsHeader = await useGeneral().getOne("donationsHeader");
// const donationsBody = await useGeneral().getOne("donationsBody");
// const programs = await queryContent("/programs").findOne();
</script>