<template>
	<ULandingSection
		id="faq"
		:title="faq.title"
		:description="faq.description"
		class="scroll-mt-[var(--header-height)]"
	>
		<ULandingFAQ
			multiple
			:items="faqItems"
			:ui="{
				button: {
					label: 'font-semibold',
					trailingIcon: {
						base: 'w-6 h-6',
					},
				},
			}"
			class="max-w-4xl mx-auto"
		/>
	</ULandingSection>
</template>
<script setup>
const faq = ref({
	title: "Frequently asked questions",
	description:
		"Culpa consectetur dolor pariatur commodo aliqua amet tempor nisi enim deserunt elit cillum.",
});
const faqItems = await queryContent("/faqs").find();
</script>