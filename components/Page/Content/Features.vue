<template>
	<div class="scrollInContainer bg-white">
		<ULandingSection
			:title="program?.features?.title"
			:description="program?.features?.description"
			:headline="program?.features?.headline"
			class="scrollInItem"
		>
			<UPageGrid
				id="features"
				class="scroll-mt-[calc(var(--header-height)+140px+128px+96px)] scrollInContainer"
			>
				<ULandingCard
					v-for="(item, index) in program?.features?.items"
					:key="index"
					v-bind="item"
					class="scrollInItem"
				/>
			</UPageGrid>
		</ULandingSection>
	</div>
</template>
<script setup>
const props = defineProps({
	program: {
		type: Object,
		required: true,
	},
});
</script>