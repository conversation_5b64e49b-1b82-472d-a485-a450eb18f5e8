<template>
	<ULandingSection
		:headline="testimonial.headline"
		:title="testimonial.title"
		:description="testimonial.description"
	>
		<UPageColumns
			id="testimonials"
			class="xl:columns-3 scroll-mt-[calc(var(--header-height)+140px+128px+96px)]"
		>
			<div
				v-for="(testimonial, index) in testimonials"
				:key="index"
				class="break-inside-avoid"
			>
				<ULandingTestimonial v-bind="testimonial" />
			</div>
		</UPageColumns>
	</ULandingSection>
</template>
<script setup>
const testimonial = ref({
	headline: "Testimonials",
	title: "What our students are saying.",
	description:
		"Proident sunt exercitation minim laborum enim laboris labore esse.",
});
const testimonials = await queryContent("/testimonials").find();
</script>