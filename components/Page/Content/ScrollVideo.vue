<template>
	<main
		class="min-h-screen flex flex-col justify-between w-full gap-8 m-8 bg-white scrollInContainer"
		:class="reverse ? 'md:flex-row-reverse' : 'md:flex-row'"
	>
		<section class="flex-1 min-h-full relative">
			<!-- <img
				:src="photo"
				class="sticky top-[170px] object-cover w-full rounded-xl scrollInItem"
			/> -->
			<XVideoScrubbing />
		</section>
		<section class="flex-1 wrapper flex flex-col gap-8">
			<article>
				<div class="scrollInItem">
					Lorem ipsum dolor sit amet consectetur adipisicing elit. Officiis
					dolore soluta modi iure ad? Quod labore alias, quaerat iste ullam
					voluptatum dolorum cupiditate suscipit doloribus esse voluptatem
					perspiciatis nostrum. Aspernatur.
				</div>
			</article>
			<article>
				<div class="scrollInItem">
					Lorem ipsum dolor sit amet consectetur adipisicing elit. Officiis
					dolore soluta modi iure ad? Quod labore alias, quaerat iste ullam
					voluptatum dolorum cupiditate suscipit doloribus esse voluptatem
					perspiciatis nostrum. Aspernatur.
				</div>
			</article>
		</section>
	</main>
</template>
<script setup>
const props = defineProps({
	reverse: {
		type: Boolean,
		default: false,
	},
	photo: {
		type: String,
		default: "/img/2023.10.13-broadcast360-01.jpg",
	},
});
</script>
<style scoped>
.wrapper article {
	@apply min-h-screen flex flex-row justify-center items-center text-4xl bg-gray-100 rounded-xl;
}
.wrapper article div {
	@apply max-w-md mx-auto;
}
</style>
