<template>
	<div>
		<main class="videoScrollOuterWrapper">
			<div class="videoScrollInnerWrapper overflow-hidden">
				<video
					src="/videos/CASE- Avid.mp4"
					class="videoScrollVideo"
					autoplay
					loop
					muted
					playsinline
				></video>
				<section class="videoScrollText">
					<!-- <div class="videoScrollTextItem">Item 1</div>
					<div class="videoScrollTextItem">Item 2</div>
					<div class="videoScrollTextItem">Item 3</div> -->
				</section>
			</div>
		</main>
	</div>
</template>

<script setup>
import { onMounted } from "vue";

onMounted(() => {
	// Get wrapper and video elements
	const videoScrollOuterWrapper = document.querySelector(
		".videoScrollOuterWrapper"
	);
	const videoScrollVideo = videoScrollOuterWrapper.querySelector("video");

	// Pause video in the beginning
	videoScrollVideo.pause();

	// Scroll function
	const videoScroll = () => {
		const videoScrollOuterWrapperPosition =
			window.scrollY - videoScrollOuterWrapper.offsetTop;
		const total = videoScrollOuterWrapper.clientHeight - window.innerHeight;

		let percentage = videoScrollOuterWrapperPosition / total;

		percentage = Math.max(0, percentage);
		percentage = Math.min(percentage, 1);

		if (videoScrollVideo.duration > 0) {
			videoScrollVideo.currentTime = videoScrollVideo.duration * percentage;
		}
	};

	videoScroll();
	window.addEventListener("scroll", videoScroll);

	// Cleanup on unmounted
	return () => {
		window.removeEventListener("scroll", videoScroll);
	};
});
</script>

<style scoped>
.videoScrollOuterWrapper {
	height: 600vh;
	position: relative;
}

.videoScrollInnerWrapper {
	position: sticky;
	top: 0;
	height: 100vh;
}

.videoScrollVideo {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.videoScrollText {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	color: #fff;
	height: 100vh;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.videoScrollTextItem {
	position: relative;
	height: 100vh;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 3rem;
	padding: 20px;
}
</style>