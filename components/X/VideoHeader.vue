<template>
	<div class="relative h-screen">
		<!-- Background Video -->
		<video
			autoplay
			muted
			loop
			playsinline
			class="absolute inset-0 object-cover w-full h-full"
		>
			<source src="/videos/CASE- Avid.mp4" type="video/mp4" />
			Your browser does not support the video tag.
		</video>

		<!-- Overlay Content -->
		<div
			class="relative z-10 flex flex-col items-center justify-center h-full text-white text-center"
		>
			<h1 class="text-4xl md:text-6xl font-bold text-focus-in fadeUp">
				{{ program?.name }}
			</h1>
			<p class="text-xl md:text-2xl mt-4 text-focus-in fadeUp">
				{{ program?.description }}
			</p>
			<div class="mt-8 flex space-x-4">
				<button
					class="bg-black bg-opacity-50 text-white px-6 py-3 rounded-lg text-lg font-semibold fadeUp"
				>
					Watch Full Video
				</button>
			</div>
		</div>

		<!-- Optional Overlay for Darkening the Video -->
		<div class="absolute inset-0 bg-black opacity-50 z-0"></div>
	</div>
</template>
<script setup>
const props = defineProps({
	program: {
		type: Object,
		required: true,
	},
});
</script>