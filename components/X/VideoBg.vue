<template>
	<div>
		<main class="videoBgOuterWrapper">
			<div class="videoBgInnerWrapper overflow-hidden">
				<video
					:src="src"
					class="videoBgVideo"
					autoplay
					loop
					muted
					playsinline
				></video>
			</div>
		</main>
	</div>
</template>

<script setup>
const props = defineProps({
	src: {
		type: String,
		required: true,
	},
});
</script>

<style scoped>
.videoBgOuterWrapper {
	height: 100vh;
	width: 100%;
	position: relative;
}

.videoBgInnerWrapper {
	position: sticky;
	top: 0;
	height: 100vh;
}

.videoBgVideo {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.videoScrollText {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	color: #fff;
	height: 100vh;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.videoScrollTextItem {
	position: relative;
	height: 100vh;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 3rem;
	padding: 20px;
}
</style>