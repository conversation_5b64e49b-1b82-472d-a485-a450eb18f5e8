<template>
	<div
		class="bg-yellow-200 flex flex-row justify-center items-center font-black bg-fixed bg-center bg-no-repeat"
		style="background-image: url('/footer/footerLogo.png')"
	>
		<div
			class="min-h-full bg-opacity-60 py-36 text-focus-in bg-gradient-to-b from-yellow-500/10 to-yellow-500/70 w-full scrollInContainer"
		>
			<section class="mx-auto max-w-screen-md text-center text-yellow-800">
				<div
					class="text-3xl scrollInItem"
					v-html="donationsHeader?.donationsHeader"
				></div>
				<div
					class="mt-4 text-base scrollInItem"
					v-html="donationsBody?.donationsBody"
				></div>
				<div scrollInItem>
					<UButton
						to="https://www.shawnigan.ca/donate-to-case"
						target="_blank"
						size="xl"
						class="mt-12"
						>Donate to C.A.S.E.</UButton
					>
				</div>
			</section>
		</div>
	</div>
</template>
<script setup>
const props = defineProps({
	name: String,
});
const donationsHeader = await useGeneral().getOne("donationsHeader");
const donationsBody = await useGeneral().getOne("donationsBody");
</script>
