<script setup>
const route = useRoute();
const props = defineProps({
  program: {
    type: Object,
    required: true,
  },
});

const photogallery = props.program?.ProgramsPhotos.map((item) => item.url);
</script>

<template>
  <main
    class="bg-stone-100 py-16 border-t-supernova border-t-2 px-6"
    v-if="photogallery.length > 0"
  >
    <section class="max-w-7xl mx-auto aspect-video py-16">
      <div class="mx-auto text-center mb-16 text-4xl font-bold">
        {{ program?.name }} In Action
      </div>
      <UCarousel
        v-slot="{ item }"
        :items="photogallery"
        :ui="{ item: 'basis-full' }"
        class="rounded-xl overflow-hidden"
        arrows
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
    </section>
  </main>
</template>
