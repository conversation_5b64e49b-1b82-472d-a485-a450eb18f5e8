<template>
  <section class="bg-white py-8 md:py-24">
    <div class="max-w-7xl mx-auto px-8">
      <!-- Audience Section -->
      <div class="text-center mb-24">
        <div
          class="text-3xl md:text-5xl font-bold text-gray-900 fadeUp"
          v-html="program?.audienceHeader"
        ></div>
        <div
          class="text-lg md:text-2xl text-gray-700 leading-relaxed mt-6 fadeUp"
          v-html="program?.audience"
        ></div>
      </div>

      <section class="flex-1 min-h-full relative">
        <img
          :src="program?.heroPhoto"
          class="object-cover w-full rounded-xl"
          alt="Program Image"
          v-if="program?.heroPhoto"
        />
      </section>

      <!-- Skills and Life Skills -->
      <!-- <div class="grid grid-cols-1 md:grid-cols-2 gap-24"> -->
      <!-- Skills Section -->
      <!-- <div class="fadeUp">
					<div
						class="text-4xl font-semibold text-gray-900 mb-8"
						v-html="program?.skillsHeader"
					></div>
					<div
						class="text-2xl text-gray-700 leading-relaxed space-y-4 p-4"
						v-html="program?.skillsBody"
					></div>
				</div> -->

      <!-- Life Skills Section -->
      <!-- <div class="fadeUp">
					<div
						class="text-4xl font-semibold text-gray-900 mb-8"
						v-html="program?.lifeSkillsHeader"
					></div>
					<div
						class="text-2xl text-gray-700 leading-relaxed space-y-4 p-4"
						v-html="program?.lifeSkillsBody"
					></div>
				</div> -->
      <!-- </div> -->
    </div>
  </section>
</template>

<script setup>
const props = defineProps({
  program: {
    type: Object,
    required: true,
  },
});
</script>
