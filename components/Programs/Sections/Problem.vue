<template>
	<section class="bg-black text-gray-300 py-24 px-8">
		<div class="max-w-7xl mx-auto text-center">
			<!-- Top Text -->
			<div
				class="text-gray-600 font-bold uppercase mb-4"
				v-html="program?.problemHeader"
			></div>
			<div
				class="text-lg md:text-4xl font-semi-bold scrollInItem"
				v-html="program?.problem"
			></div>

			<!-- Key Features -->
			<div
				class="mt-12 flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8"
			></div>

			<!-- Image -->
			<div class="mt-12">
				<img
					:src="program?.problemPhoto"
					class="mx-auto scrollInItem rounded-2xl"
				/>
			</div>
		</div>
	</section>
</template>
<script setup>
const props = defineProps({
	program: {
		type: Object,
		required: true,
	},
});
</script>