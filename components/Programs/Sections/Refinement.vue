<template>
  <section class="bg-gray-100 py-16">
    <div class="max-w-7xl mx-auto px-6">
      <!-- Refinement Header -->
      <div class="text-center mb-12">
        <div
          class="text-4xl font-bold text-gray-900 fadeUp"
          v-html="program?.refinementHeader"
        ></div>
        <div
          class="text-lg text-gray-700 leading-relaxed mt-4 fadeUp"
          v-html="program?.refinement"
        ></div>
      </div>

      <!-- Visual and Text Content -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <!-- Video Placeholder -->
        <div class="fadeUp">
          <video controls class="w-full rounded-lg shadow-lg scrollInItem">
            <source :src="program?.altVideo" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>

        <!-- Image and Text -->
        <div class="space-y-6">
          <div class="text-center md:text-left">
            <div
              class="text-3xl font-semibold text-gray-800 mb-4"
              v-html="program?.careersHeader"
            ></div>
            <div class="pt-8" v-html="program?.careersBody"></div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const props = defineProps({
  program: {
    type: Object,
    required: true,
  },
});
</script>
