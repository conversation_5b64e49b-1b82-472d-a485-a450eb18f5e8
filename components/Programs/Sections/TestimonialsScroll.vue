<template>
	<main
		class="min-h-screen flex flex-col justify-between gap-8 m-8 bg-white scrollInContainer mx-auto"
		:class="reverse ? 'md:flex-row-reverse' : 'md:flex-row'"
	>
		<!-- Image Section -->
		<section class="flex-1 min-h-full relative">
			<img
				:src="program?.photo"
				class="sticky top-[170px] object-cover w-full rounded-xl"
				alt="Program Image"
			/>
		</section>

		<!-- Content Section -->
		<section class="flex-1 wrapper flex flex-col gap-8">
			<article
				v-for="(testimonial, index) in program?.testimonials"
				:key="index"
			>
				<div class="scrollInItem">
					<blockquote class="text-xl text-gray-800">
						“{{ testimonial?.quote }}”
					</blockquote>
					<p class="mt-4 text-lg font-semibold text-gray-700">
						- {{ testimonial?.name }}, {{ testimonial?.title }}
					</p>
				</div>
			</article>
		</section>
	</main>
</template>

<script setup>
const props = defineProps({
	reverse: {
		type: Boolean,
		default: false,
	},
	photo: {
		type: String,
		default: "/img/2023.10.13-broadcast360-01.jpg",
	},
	program: {
		type: Object,
		required: true,
	},
});
</script>

<style scoped>
.wrapper article {
	@apply min-h-screen flex flex-row justify-center items-center text-4xl bg-gray-100 rounded-xl;
}
.wrapper article div {
	@apply max-w-md mx-auto;
}
</style>