<template>
  <main
    class="min-h-screen flex flex-col justify-between gap-8 m-8 bg-white scrollInContainer mx-auto max-w-7xl px-6"
    :class="reverse ? 'md:flex-row-reverse' : 'md:flex-row'"
  >
    <!-- Image Section -->
    <section class="flex-1 min-h-full relative">
      <img
        :src="program?.heroPhoto"
        class="sticky top-[240px] object-cover w-full rounded-xl"
        alt="Program Image"
        v-if="program?.heroPhoto"
      />
    </section>

    <!-- Content Section -->
    <section class="flex-1 wrapper flex flex-col gap-8">
      <article
        v-for="(section, index) in program?.ProgramSections"
        :key="index"
        class=""
      >
        <div>
          <main class="rounded-xl border">
            <img :src="section.photo" class="rounded-t-xl" />
            <section class="text-left p-6">
              <div class="text-4xl pb-4" v-html="section?.title"></div>
              <div class="pt-1" v-html="section?.body"></div>
            </section>
          </main>
        </div>
      </article>
    </section>
  </main>
</template>

<script setup>
const props = defineProps({
  reverse: {
    type: Boolean,
    default: false,
  },
  altPhoto: {
    type: String,
    default: "/img/2023.10.13-broadcast360-01.jpg",
  },
  program: {
    type: Object,
    required: true,
  },
});
</script>

<style scoped>
.wrapper article {
  @apply md:pb-16 flex flex-row rounded-xl;
}
/*
.wrapper article div {
	@apply max-w-md mx-auto;
} */
</style>
