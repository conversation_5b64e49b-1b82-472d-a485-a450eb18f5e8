<template>
  <main class="">
    <section
      v-for="(section, index) in program?.ProgramSections"
      :key="index"
      :class="backgroundColor(index)"
    >
      <div
        class="max-w-7xl mx-auto px-8 lg:flex lg:py-20"
        :class="flexColOrder(index)"
      >
        <div class="flex-1 md:mx-4 mx-auto text-center lg:text-left">
          <div
            class="py-8 md:py-10 text-3xl font-black text-gray-800 md:text-4xl lg:text-5xl"
            v-html="section?.title"
          ></div>

          <div
            class="prose mx-auto mb-12 max-w-xl text-gray-500 text-lg lg:prose-xl lg:mb-0"
            v-html="section?.body"
          ></div>
        </div>
        <div class="flex-1 items-center mx-4">
          <div class="py-10 flex justify-center">
            <img
              :src="section.photo"
              class="rounded-lg"
              :alt="section?.title"
            />
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<script setup>
const props = defineProps({
  reverse: {
    type: Boolean,
    default: false,
  },
  altPhoto: {
    type: String,
    default: "/img/2023.10.13-broadcast360-01.jpg",
  },
  program: {
    type: Object,
    required: true,
  },
});
const flexColOrder = function (index) {
  if (index % 2 != 0) {
    return "flex-wrap-reverse flex-row-reverse bg-gray-100 gap-24";
  } else {
    return "flex-wrap flex-row bg-white gap-24";
  }
};
const backgroundColor = function (index) {
  if (index % 2 != 0) {
    return "bg-gray-100";
  } else {
    return "bg-white";
  }
};
</script>

<style scoped>
.wrapper article {
  @apply md:pb-16 flex flex-row rounded-xl;
}
/*
  .wrapper article div {
      @apply max-w-md mx-auto;
  } */
</style>
