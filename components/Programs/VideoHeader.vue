<template>
  <div class="relative h-screen">
    <!-- Background Video -->
    <video
      autoplay
      muted
      loop
      playsinline
      class="absolute inset-0 object-cover w-full h-full"
      v-if="randomVideo"
    >
      <source :src="randomVideo" type="video/mp4" />
      Your browser does not support the video tag.
    </video>

    <!-- Overlay Content -->
    <div
      class="relative z-10 flex flex-col items-center justify-center h-full text-white text-center fadeUp"
    >
      <!-- temporary conditional for woodworking until added to cms -->
      <div
        v-if="program?.isComingSoon == true"
        class="bg-gray-500 text-white px-5 py-3 rounded text-2xl my-10"
      >
        COMING SOON
      </div>

      <div
        class="text-4xl md:text-6xl font-black text-focus-in fadeUp"
        v-html="program.heroName"
      ></div>
      <div
        class="text-xl md:text-2xl mt-4 text-focus-in fadeUp text-gray-400 max-w-5xl"
        v-html="program.heroDescription"
      ></div>
      <div class="mt-8 flex space-x-4">
        <button
          class="bg-black bg-opacity-50 text-white px-6 py-3 rounded-lg text-lg font-semibold fadeUp"
          @click="isOpen = true"
        >
          Exper<span class="font-light">IENCE</span>
        </button>
      </div>
    </div>

    <!-- video modal -->

    <UModal v-model="isOpen" fullscreen class="relative">
      <UCard
        :ui="{
          base: 'flex flex-col border-none absolute inset-0 overflow-hidden',
          rounded: '',
          body: {
            base: 'grow border-none',
          },
          footer: {
            base: 'border-none',
          },
        }"
        class="dark bg-gray-900"
      >
        <div
          class="flex items-center justify-between max-w-screen-2xl mx-auto relative z-20 bg-gray-900"
        >
          <div
            class="text-base font-semibold text-gray-950 dark:text-white uppercase"
            :class="`transition transform duration-100 ease-out animate-fadeUp`"
          >
            {{ program?.name }}
          </div>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="isOpen = false"
          />
        </div>

        <section class="m-10">
          <video
            autoplay
            loop
            playsinline
            class="absolute inset-0 object-cover w-full h-full z-10 mt-20"
            v-if="featuredVideo"
          >
            <source :src="program?.altVideo" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </section>
      </UCard>
    </UModal>
    <!-- Optional Overlay for Darkening the Video -->
    <div class="absolute inset-0 bg-black opacity-50 z-0"></div>
  </div>
</template>
<script setup>
const props = defineProps({
  program: {
    type: Object,
    required: true,
  },
});
const isOpen = ref(false);
// computed method pick random from program?.headerVideos
const randomVideo = computed(() => {
  const videos = props.program?.headerVideos;
  return videos[Math.floor(Math.random() * videos.length)];
});
const featuredVideo = computed(() => {
  let video = props.program.featuredVideo;
  if (video == null) {
    video = props.program?.headerVideos[0];
  }
  return video;
});
</script>
