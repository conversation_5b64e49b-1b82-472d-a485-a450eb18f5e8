<template>
  <XVideoBg
    class="fixed w-full top-0 left-0 z-10"
    :src="landing?.landingBgVideo"
  />
  <div
    class="bg-gray-900 bg-opacity-60 absolute z-30 top-0 right-0 left-0 min-h-full"
  >
    <section class="mt-40 relative z-40">
      <HeaderNavigationFixed />
      <!-- <HeaderHomePageContent :content="landing" /> -->
    </section>
  </div>
  <!-- <PageContentTestimonials />
	<PageContentFAQs /> -->
</template>
<script setup lang="ts">
definePageMeta({
  layout: "home",
});
const landing = await useGeneral().get();
</script>
<style lang="css" scoped>
.videoBgVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
