<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Debug Information</h1>
    
    <div class="mb-4">
      <h2 class="text-lg font-semibold">Runtime Config:</h2>
      <pre class="bg-gray-100 p-4 rounded">{{ JSON.stringify(config, null, 2) }}</pre>
    </div>
    
    <div class="mb-4">
      <h2 class="text-lg font-semibold">API URL:</h2>
      <p class="bg-gray-100 p-2 rounded">{{ apiUrl }}</p>
    </div>
    
    <div class="mb-4">
      <h2 class="text-lg font-semibold">General Endpoint:</h2>
      <p class="bg-gray-100 p-2 rounded">{{ generalEndpoint }}</p>
    </div>
    
    <div class="mb-4">
      <h2 class="text-lg font-semibold">Programs Endpoint:</h2>
      <p class="bg-gray-100 p-2 rounded">{{ programsEndpoint }}</p>
    </div>
    
    <div class="mb-4">
      <button 
        @click="testGeneralAPI" 
        class="bg-blue-500 text-white px-4 py-2 rounded mr-2"
        :disabled="loading"
      >
        Test General API
      </button>
      
      <button 
        @click="testProgramsAPI" 
        class="bg-green-500 text-white px-4 py-2 rounded"
        :disabled="loading"
      >
        Test Programs API
      </button>
    </div>
    
    <div v-if="loading" class="mb-4">
      <p>Loading...</p>
    </div>
    
    <div v-if="result" class="mb-4">
      <h2 class="text-lg font-semibold">API Result:</h2>
      <pre class="bg-gray-100 p-4 rounded">{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
    
    <div v-if="error" class="mb-4">
      <h2 class="text-lg font-semibold text-red-600">Error:</h2>
      <pre class="bg-red-100 p-4 rounded text-red-800">{{ error }}</pre>
    </div>
  </div>
</template>

<script setup>
const config = useRuntimeConfig()
const loading = ref(false)
const result = ref(null)
const error = ref(null)

const apiUrl = computed(() => config.public.apiURL)
const generalEndpoint = computed(() => config.public.apiURL + "/general")
const programsEndpoint = computed(() => config.public.apiURL + "/programs")

const testGeneralAPI = async () => {
  loading.value = true
  result.value = null
  error.value = null
  
  try {
    const data = await useGeneral().get()
    result.value = data
  } catch (err) {
    error.value = err.toString()
  } finally {
    loading.value = false
  }
}

const testProgramsAPI = async () => {
  loading.value = true
  result.value = null
  error.value = null
  
  try {
    const data = await usePrograms().getAll()
    result.value = data
  } catch (err) {
    error.value = err.toString()
  } finally {
    loading.value = false
  }
}
</script>
