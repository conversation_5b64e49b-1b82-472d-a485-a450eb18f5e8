<template>
	<main class="max-w-xl mx-auto my-4">
		<FormKit
			type="form"
			@submit="submitHandler"
			:value="form"
			#default="{ form }"
		>
			<FormKit
				type="text"
				name="firstName"
				label="First Name"
				placeholder=""
				help=""
				validation="required"
			/>
			<hr />
			<FormKit
				type="checkbox"
				name="programs"
				label="Programs"
				:options="casePrograms"
				validation="required"
			/>
		</FormKit>
	</main>
</template>
<script setup>
const submitHandler = async (form) => {};
const casePrograms = [
	"Broadcast Journalism",
	"Coding",
	"Digital Imagery",
	"Drama Theater",
	"Drone Development",
	"Film Making",
	"Fine Arts",
	"Gaming Development",
	"Photography",
	"Recording Arts",
	"Robotics",
	"VOTEC",
];
</script>