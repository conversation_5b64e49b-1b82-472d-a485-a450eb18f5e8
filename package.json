{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@formkit/addons": "^1.6.3", "@formkit/icons": "^1.6.3", "@formkit/nuxt": "^1.6.3", "@formkit/pro": "npm:@formkit-enterprise/pro@^0.122.21", "@formkit/themes": "^1.6.3", "@iconify-json/heroicons": "^1.1.21", "@iconify-json/simple-icons": "^1.1.101", "@marker.io/browser": "^0.19.0", "@nuxt/content": "^2.12.0", "@nuxt/image": "^1.7.0", "@nuxt/ui": "^2.16.0", "@nuxt/ui-pro": "^1.2.0", "nuxt": "^3.16.1", "vue": "^3.4.27", "vue-router": "^4.3.2"}, "devDependencies": {"@iconify-json/gridicons": "^1.1.14", "@iconify-json/iconoir": "^1.1.44", "@iconify-json/material-symbols-light": "^1.1.24", "@nuxtjs/google-fonts": "^3.2.0", "@tailwindcss/typography": "^0.5.16"}}