export default defineAppConfig({
  ui: {
    primary: "yellow",
    page: {
      header: {
        title: "text-yellow-500",
      },
      body: {
        wrapper: "bg-white rounded-2xl p-4 mb-8",
      },
    },
    title: "text-yellow-500",
    gray: "zinc",
    notifications: {
      // Show toasts at the top right of the screen
      position: "bottom-10 top-auto",
    },
  },
  shawniganTopNav: [
    {
      label: "Apply Now",
      icon: "",
      to: "https://shawnigan.myschoolapp.com/app#login/apply",
      target: "_blank",
    },
    {
      label: "Contact",
      icon: "",
      to: "page/about/contact",
    },
    {
      label: "Login",
      icon: "",
      to: "app",
    },
    {
      label: "Blog",
      icon: "",
      to: "page/a-voice-in-the-wilderness-blog",
    },
  ],
  shawniganMenuItems: [
    {
      label: "About",
      icon: "",
      to: "about",
      submenu: [
        { label: "Head's Welcome", icon: "", to: "about/heads-welcome" },
        { label: "School profile", icon: "", to: "about/school-profile" },
        { label: "Mission & Values", icon: "", to: "about/mission--values" },
        { label: "Strategic Plan", icon: "", to: "about/strategic-plan" },
        { label: "School history", icon: "", to: "about/school-history" },
        {
          label: "Shawnigan in 110 Objects",
          icon: "",
          to: "about/shawnigan-in-110-objects",
        },
        { label: "School Museum", icon: "", to: "about/school-museum" },
        { label: "Careers", icon: "", to: "about/careers" },
        { label: "Facility Rentals", icon: "", to: "about/facility-rentals" },
        {
          label: "Accessibility Plan",
          icon: "",
          to: "about/accessibility-plan",
        },
        { label: "Contact", icon: "", to: "about/contact" },
      ],
    },
    {
      label: "Admissions",
      icon: "",
      to: "admissions",
      submenu: [
        { label: "Welcome", icon: "", to: "admissions/welcome" },
        {
          label: "Connect with Admissions",
          icon: "",
          to: "admissions/connect-with-admissions",
        },
        {
          label: "Virtual Tour",
          icon: "",
          to: "https://circlescapes.biz/shawniganlake/360tour",
          target: "_blank",
        },
        { label: "Financial Aid", icon: "", to: "admissions/financial-aid" },
        {
          label: "Entrance Awards",
          icon: "",
          to: "admissions/entrance-awards",
        },
        {
          label: "Tuition & Payments",
          icon: "",
          to: "admissions/tuition--payments",
        },
        {
          label: "Legacy Students",
          icon: "",
          to: "admissions/legacy-students",
        },
        { label: "FAQ", icon: "", to: "admissions/faq" },
      ],
    },
    {
      label: "Student Life",
      icon: "",
      to: "student-life",
      submenu: [
        { label: "Welcome", icon: "", to: "student-life/welcome" },
        {
          label: "The Shawnigan Journey",
          icon: "",
          to: "student-life/the-shawnigan-journey",
        },
        {
          label: "I am Shawnigan",
          icon: "",
          to: "student-life/i-am-shawnigan",
        },
        { label: "Houses", icon: "", to: "student-life/houses" },
        {
          label: "Grade 8 Experience",
          icon: "",
          to: "student-life/grade-8-experience",
        },
        { label: "Belonging", icon: "", to: "student-life/belonging" },
        {
          label: "Safeguarding Procedures",
          icon: "",
          to: "student-life/safeguarding-procedures",
        },
      ],
    },
    {
      label: "Academics",
      icon: "",
      to: "academics",
      submenu: [
        { label: "Courses", icon: "", to: "academics/courses" },
        {
          label: "Beyond the Gates",
          icon: "",
          to: "academics/beyond-the-gates",
        },
        {
          label: "University Guidance",
          icon: "",
          to: "academics/university-guidance",
        },
        {
          label: "Learning Centre",
          icon: "",
          to: "academics/learning-centre",
        },
        {
          label: "English Language Support",
          icon: "",
          to: "academics/english-language-support",
        },
        {
          label: "French Immersion",
          icon: "",
          to: "academics/french-immersion",
        },
        { label: "SOUL Seeking", icon: "", to: "academics/soul-seeking" },
      ],
    },
    {
      label: "Programs",
      icon: "",
      to: "page/programs/arts-360",
      submenu: [
        { label: "Arts - 360", icon: "", to: "programs/arts-360" },
        { label: "Athletics", icon: "", to: "programs/athletics" },
        {
          label: "Applied Arts – C.A.S.E.",
          icon: "",
          to: "programs/applied-arts-case",
        },
        {
          label: "Experiential Learning",
          icon: "",
          to: "programs/experiential-learning",
        },
        { label: "Summer Camps", icon: "", to: "programs/summer-camps" },
        {
          label: "Global Exchange Program",
          icon: "",
          to: "programs/global-exchange-program",
        },
      ],
    },
    {
      label: "Support",
      icon: "",
      to: "page/support/welcome",
      submenu: [
        { label: "Welcome", icon: "", to: "support/welcome" },
        { label: "Donate Now", icon: "", to: "support/donate-now" },
        { label: "Annual Fund", icon: "", to: "support/annual-fund" },
        { label: "Samuel House", icon: "", to: "support/samuel-house" },
        { label: "Legacy Giving", icon: "", to: "support/legacy-giving" },
        {
          label: "Rugby Pavilion Pathway Stones",
          icon: "",
          to: "support/rugby-pavilion-pathway-stones",
        },
        { label: "Impact Report", icon: "", to: "support/impact-report" },
        { label: "FAQ", icon: "", to: "support/faq" },
      ],
    },
    {
      label: "Alumni",
      icon: "",
      to: "alumni",
      submenu: [
        { label: "Welcome", icon: "", to: "alumni/welcome" },
        {
          label: "Alumni News/Profiles",
          icon: "",
          to: "alumni/alumni-newsprofiles",
        },
        { label: "Alumni Events", icon: "", to: "alumni/alumni-events" },
        {
          label: "Graham L. Anderson Award for Alumni Scholars",
          icon: "",
          to: "alumni/graham-l-anderson-award-for-alumni-scholars",
        },
        {
          label: "Shawnigan Connect",
          icon: "",
          to: "alumni/shawnigan-connect",
        },
        {
          label: "Update Contact Info",
          icon: "",
          to: "alumni/update-contact-info",
        },
        {
          label: "Online Commissary",
          icon: "",
          to: "alumni/online-commissary",
        },
      ],
    },
    {
      label: "News",
      icon: "",
      to: "news",
      submenu: [
        { label: "News & updates", icon: "", to: "news/news--updates" },
        { label: "Calendar", icon: "", to: "news/calendar" },
        { label: "Publications", icon: "", to: "news/publications" },
      ],
    },
  ],
});
