// Get wrapper and video elements
const videoScrollOuterWrapper = document.querySelector('.videoScrollOuterWrapper');
const videoScrollVideo = videoScrollOuterWrapper.querySelector('video');

// Pause video in the beginning
videoScrollVideo.pause();

// Scroll function
const videoScroll = () => {
  const videoScrollOuterWrapperPosition = window.scrollY - videoScrollOuterWrapper.offsetTop;
  const total = videoScrollOuterWrapper.clientHeight - window.innerHeight;

  let percentage = videoScrollOuterWrapperPosition / total;


  percentage = Math.max(0, percentage);
  percentage = Math.min(percentage, 1);
  if (videoScrollVideo.duration > 0) {
    videoScrollVideo.currentTime = videoScrollVideo.duration * percentage;
  }
}

videoScroll();
window.addEventListener('scroll', videoScroll);