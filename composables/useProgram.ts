export function usePrograms() {
  const config = useRuntimeConfig();
  const endpoint = config.public.apiURL + "/programs";

  const getAll = async () => {
    try {
      console.log("Fetching programs from endpoint:", endpoint);
      const programs = await $fetch(endpoint);
      console.log("Successfully fetched programs:", programs);
      return programs;
    } catch (error) {
      console.error("Error fetching programs:", error);
      throw error;
    }
  };

  const get = async (slug) => {
    try {
      const url = `${endpoint}/slug/${slug}`;
      console.log("Fetching program from endpoint:", url);
      const program = await $fetch(url);
      console.log("Successfully fetched program for slug:", slug, program);
      return program;
    } catch (error) {
      console.error("Error fetching program for slug:", slug, error);
      throw error;
    }
  };

  return {
    getAll,
    get,
  };
}
