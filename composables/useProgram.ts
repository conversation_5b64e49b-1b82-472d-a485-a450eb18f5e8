export function usePrograms() {
  const config = useRuntimeConfig();
  // Use proxy endpoint in development, direct API in production
  const endpoint = import.meta.dev
    ? "/api/programs"
    : config.public.apiURL + "/programs";

  const getAll = async () => {
    try {
      const programs = await $fetch(endpoint);
      return programs;
    } catch (error) {
      throw error;
    }
  };

  const get = async (slug: string) => {
    try {
      const url = `${endpoint}/slug/${slug}`;
      const program = await $fetch(url);
      return program;
    } catch (error) {
      throw error;
    }
  };

  return {
    getAll,
    get,
  };
}
