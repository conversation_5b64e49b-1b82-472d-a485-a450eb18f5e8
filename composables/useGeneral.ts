export function useGeneral() {
  const config = useRuntimeConfig();
  const endpoint = config.public.apiURL + "/general";

  const get = async () => {
    try {
      console.log("Fetching from endpoint:", endpoint);
      const general = await $fetch(endpoint);
      console.log("Successfully fetched general data:", general);
      return general;
    } catch (error) {
      console.error("Error fetching general data:", error);
      throw error;
    }
  };

  const getOne = async (slug) => {
    try {
      const url = `${endpoint}/${slug}`;
      console.log("Fetching from endpoint:", url);
      const data = await $fetch(url);
      console.log("Successfully fetched data for slug:", slug, data);
      return data;
    } catch (error) {
      console.error("Error fetching data for slug:", slug, error);
      throw error;
    }
  };

  return {
    get,
    getOne,
  };
}
