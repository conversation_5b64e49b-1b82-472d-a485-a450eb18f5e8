export function useGeneral() {
  const config = useRuntimeConfig();
  // Use proxy endpoint in development, direct API in production
  const endpoint = import.meta.dev
    ? "/api/general"
    : config.public.apiURL + "/general";

  const get = async () => {
    try {
      const general = await $fetch(endpoint);
      return general;
    } catch (error) {
      throw error;
    }
  };

  const getOne = async (slug: string) => {
    try {
      const url = `${endpoint}/${slug}`;
      const data = await $fetch(url);
      return data;
    } catch (error) {
      throw error;
    }
  };

  return {
    get,
    getOne,
  };
}
