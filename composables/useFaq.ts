export function useFaqs() {
  const config = useRuntimeConfig();
  // Use proxy endpoint in development, direct API in production
  const endpoint = import.meta.dev
    ? "/api/faqs"
    : config.public.apiURL + "/faqs";

  const getAll = async () => {
    try {
      const faqs = await $fetch(endpoint);
      return faqs;
    } catch (error) {
      throw error;
    }
  };

  const get = async (slug: string) => {
    try {
      const url = `${endpoint}/slug/${slug}`;
      const faq = await $fetch(url);
      return faq;
    } catch (error) {
      throw error;
    }
  };

  return {
    getAll,
    get,
  };
}
