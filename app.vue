<template>
	<div>
		<NuxtLoadingIndicator />

		<NuxtLayout>
			<NuxtPage />
		</NuxtLayout>

		<UNotifications />
		<UModals />
	</div>
</template>
<script setup>
useSeoMeta({
	titleTemplate: `%s - Case`,
});
import markerSDK from "@marker.io/browser";
import { ref, onMounted } from "vue";
onMounted(() => {
	const widget = markerSDK.loadWidget({
		project: "66ae82dea31edd124f9a6ca0",
	});
});
</script>

<style >
/* Reset */
body {
	width: 100%;
	height: 100vh;
	margin: 0;
	padding: 0;
}
.scrollInContainer {
	/* width: 100%;
	display: flex;
	align-items: center;
	flex-wrap: wrap; */
}

/* Scroll */
@keyframes appear {
	from {
		opacity: 0;
		scale: 0.2;
	}
	to {
		opacity: 1;
		scale: 1;
	}
}

.scrollInItem {
	animation: appear linear;
	animation-timeline: view();
	animation-range: entry 0% cover 45%;
}

/* ----------------------------------------------
 * Generated by Animista on 2024-7-23 16:20:57
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */
.text-focus-in {
	-webkit-animation: text-focus-in 0.75s cubic-bezier(0.55, 0.085, 0.68, 0.53)
		both;
	animation: text-focus-in 0.75s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;
}
/* ----------------------------------------------
 * Generated by Animista on 2024-7-23 16:20:57
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

@-webkit-keyframes text-focus-in {
	0% {
		-webkit-filter: blur(12px);
		filter: blur(12px);
		opacity: 0;
	}
	100% {
		-webkit-filter: blur(0);
		filter: blur(0);
		opacity: 1;
	}
}
@keyframes text-focus-in {
	0% {
		-webkit-filter: blur(12px);
		filter: blur(12px);
		opacity: 0;
	}
	100% {
		-webkit-filter: blur(0);
		filter: blur(0);
		opacity: 1;
	}
}
</style>
