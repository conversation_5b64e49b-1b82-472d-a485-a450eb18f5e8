import { genesisIcons } from "@formkit/icons";
import { defineFormKitConfig } from "@formkit/vue";
import { createAutoHeightTextareaPlugin } from "@formkit/addons";
import {
  createProPlugin,
  inputs,
  toggle,
  dropdown,
  datepicker,
  taglist,
} from "@formkit/pro";
import { rootClasses } from "./formkit.theme";

const pro = createProPlugin("fk-55f4faf175", {
  inputs,
  toggle,
  dropdown,
  datepicker,
  taglist,
});

export default defineFormKitConfig({
  plugins: [pro, createAutoHeightTextareaPlugin()],
  icons: { ...genesisIcons },
  config: {
    rootClasses,
  },
});
